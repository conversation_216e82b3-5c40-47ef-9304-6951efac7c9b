package com.jp.med.mmis.modules.matReceipt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;

import java.util.List;
import java.util.Map;

/**
 * 物资入库
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 15:01:07
 */
public interface MmisAsetStorageReadService extends IService<MmisAsetStorageDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<MmisAsetStorageVo> queryList(MmisAsetStorageDto dto);

    MmisAsetStorageVo queryDocmentNum(MmisAsetStorageDto dto);

    MmisAsetStorageVo queryAppAuditDetail(MmisAsetStorageDto dto);

    MmisAsetStorageVo queryDocmentNumStr(MmisAsetStorageDto dto);

    List<MmisAsetStorageDetailVo> hasStroagedMats(MmisAsetStorageDto dto);

    List<MmisAsetStorageDetailVo> hasStroagedMatsInApply(MmisAsetStorageDto dto);

    /**
     * 查询库存物资信息，使用matUniqueCode作为key返回
     *
     * @param dto 查询参数
     * @return 物资库存列表
     */
    List<MmisAsetStorageDetailVo> hasStroagedMatsInApplyByUniqueCode(MmisAsetStorageDto dto);

    List<MmisAsetStorageDetailVo> queryStorageRecordsByMat2Code(MmisAsetStorageDto dto);

    List<MmisAsetStorageVo> queryWaitAccountingReceipt(MmisAsetStorageDto dto);

    /**
     * 查询指定时间段内待结账的入库记录（用于结账预览）
     *
     * @param dto 查询参数，包含startDate、endDate等
     * @return 入库记录列表及汇总数据
     */
    List<MmisAsetStorageVo> queryPendingStorageByPeriod(MmisAsetStorageDto dto);

    List<MmisAsetStorageVo> waitGoReimReceipt(MmisAsetStorageDto dto);

    /**
     * 查询入库明细数据
     *
     * @param dto 查询参数
     * @return 入库明细列表
     */
    List<MmisAsetStorageDetailVo> queryStorageDetailList(MmisAsetStorageDto dto);

    /**
     * 信息科入库明细查询
     *
     * @param dto 查询参数
     * @return 信息科入库明细列表
     */
    List<MmisAsetStorageDetailVo> queryXinxiStorageDetailList(MmisAsetStorageDto dto);
}
