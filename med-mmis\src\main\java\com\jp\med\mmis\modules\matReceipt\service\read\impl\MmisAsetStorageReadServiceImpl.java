package com.jp.med.mmis.modules.matReceipt.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.mmis.modules.common.service.read.MmisAuditRcdfmReadService;
import com.jp.med.mmis.modules.common.vo.MmisAuditRcdfmVo;
import com.jp.med.mmis.modules.exportFileManage.mapper.read.MmisOutboundExportRecordReadMapper;
import com.jp.med.mmis.modules.exportFileManage.vo.MmisOutboundExportRecordVo;
import com.jp.med.mmis.modules.matReceipt.mapper.read.MmisAsetStorageDetailReadMapper;
import com.jp.med.mmis.modules.matReceipt.mapper.read.MmisInvoRcdReadMapper;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jp.med.mmis.modules.matReceipt.mapper.read.MmisAsetStorageReadMapper;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;
import com.jp.med.mmis.modules.matReceipt.service.read.MmisAsetStorageReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class MmisAsetStorageReadServiceImpl extends ServiceImpl<MmisAsetStorageReadMapper, MmisAsetStorageDto>
        implements MmisAsetStorageReadService {

    @Autowired
    private MmisAsetStorageReadMapper mmisAsetStorageReadMapper;

    @Autowired
    private MmisAuditRcdfmReadService auditRcdfmReadService;

    @Autowired
    private MmisAsetStorageDetailReadMapper storageDetailReadMapper;

    @Autowired
    private MmisOutboundExportRecordReadMapper exportRecordReadMapper;

    @Autowired
    private MmisInvoRcdReadMapper invoRcdReadMapper;

    @Override
    public List<MmisAsetStorageVo> queryList(MmisAsetStorageDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (!MedConst.DEV_NAME.equals(dto.getUsername())) {
            if (StringUtils.isNotEmpty(dto.getAudit())) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                // 给入库执行查询留后门：不是审核页面，也不加必须是crter的限制
                if (StringUtils.isEmpty(dto.getInStatus())) {
                    dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
                }
            }
        }
        // 为了历史记录回显：如果chkState == 3 ，就是查询历史记录，就放开所有数据
        if (AuditConst.RES_IN.equals(dto.getChkState())) {
            dto.setChkState(null);
        }
        List<MmisAsetStorageVo> mmisAsetStorageVos = mmisAsetStorageReadMapper.queryList(dto);
        // 遍历集合，给每条数据查询入库详情得到品名name * 入库数量num ，把字符串给invtsummary
        for (MmisAsetStorageVo mmisAsetStorageVo : mmisAsetStorageVos) {
            MmisOutboundExportRecordVo mmisOutboundExportRecordVo = exportRecordReadMapper
                    .queryListByReceiptId(mmisAsetStorageVo.getId());
            mmisAsetStorageVo.setExportStatus(!Objects.isNull(mmisOutboundExportRecordVo)
                    && StringUtils.isNotEmpty(mmisOutboundExportRecordVo.getExportStatus())
                            ? mmisOutboundExportRecordVo.getExportStatus()
                            : MedConst.TYPE_0);

            List<MmisAsetStorageDetailVo> storageDetailVos = storageDetailReadMapper
                    .queryListByApplyId(mmisAsetStorageVo.getId());
            StringBuilder invtSummary = new StringBuilder();

            for (MmisAsetStorageDetailVo storageDetailVo : storageDetailVos) {
                // 拼接品名和数量，品名*数量 为一对，上下两对之间用;拼接
                invtSummary.append(storageDetailVo.getName()).append("*").append(storageDetailVo.getNum()).append(";");
            }
            // 把结果给mmisAsetStorageVo的invtsummary
            mmisAsetStorageVo.setInvtSummary(invtSummary.toString());
        }

        return mmisAsetStorageVos;
    }

    @Override
    public List<MmisAsetStorageVo> waitGoReimReceipt(MmisAsetStorageDto dto) {
        if (!MedConst.DEV_NAME.equals(dto.getUsername())) {
            if (StringUtils.isNotEmpty(dto.getAudit())) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                // 给入库执行查询留后门：不是审核页面，也不加必须是crter的限制
                if (StringUtils.isEmpty(dto.getInStatus())) {
                    dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
                }
            }
            
            // 新增权限控制：对于已处理入库单(reimStatusFlag='0')和已报销入库单(reimStatusFlag='1')
            // 限制当前用户只能查看到自己处理的记录
            if ("0".equals(dto.getReimStatusFlag()) || "1".equals(dto.getReimStatusFlag())) {
                // 获取当前员工编号
                com.jp.med.common.entity.user.HrmUser hrmUser = dto.getSysUser().getHrmUser();
                String currentEmpCode = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() 
                        : dto.getSysUser().getUsername();
                
                // 设置处理人过滤条件，只显示当前用户处理的记录
                if (StringUtils.isNotEmpty(currentEmpCode)) {
                    dto.setProcessEmpCode(currentEmpCode);
                }
            }
        }
        // 为了历史记录回显：如果chkState == 3 ，就是查询历史记录，就放开所有数据
        if (AuditConst.RES_IN.equals(dto.getChkState())) {
            dto.setChkState(null);
        }
        List<MmisAsetStorageVo> mmisAsetStorageVos = mmisAsetStorageReadMapper.queryWaitGoReimList(dto);
        // 遍历集合，给每条数据查询入库详情得到品名name * 入库数量num ，把字符串给invtsummary
        for (MmisAsetStorageVo mmisAsetStorageVo : mmisAsetStorageVos) {
            MmisOutboundExportRecordVo mmisOutboundExportRecordVo = exportRecordReadMapper
                    .queryListByReceiptId(mmisAsetStorageVo.getId());
            mmisAsetStorageVo.setExportStatus(!Objects.isNull(mmisOutboundExportRecordVo)
                    && StringUtils.isNotEmpty(mmisOutboundExportRecordVo.getExportStatus())
                            ? mmisOutboundExportRecordVo.getExportStatus()
                            : MedConst.TYPE_0);

            List<MmisAsetStorageDetailVo> storageDetailVos = storageDetailReadMapper
                    .queryListByApplyId(mmisAsetStorageVo.getId());
            StringBuilder invtSummary = new StringBuilder();

            for (MmisAsetStorageDetailVo storageDetailVo : storageDetailVos) {
                // 拼接品名和数量，品名*数量 为一对，上下两对之间用;拼接
                invtSummary.append(storageDetailVo.getName()).append("*").append(storageDetailVo.getNum()).append(";");
            }
            // 把结果给mmisAsetStorageVo的invtsummary
            mmisAsetStorageVo.setInvtSummary(invtSummary.toString());

            // 获取发票文件路径
            if (StringUtils.isNotEmpty(mmisAsetStorageVo.getInvoId())) {
                // 分割发票ID
                String[] invoiceIds = mmisAsetStorageVo.getInvoId().split(",");
                List<String> filePaths = new ArrayList<>();
                List<String> fileNames = new ArrayList<>();

                // 查询每个发票ID对应的文件路径和名称
                for (String invoiceId : invoiceIds) {
                    if (StringUtils.isNotEmpty(invoiceId)) {
                        // 调用发票mapper查询文件路径和名称
                        MmisInvoRcdVo invoice = invoRcdReadMapper.getInvoiceInfo(invoiceId.trim());
                        if (invoice != null) {
                            if (StringUtils.isNotEmpty(invoice.getAtt())) {
                                filePaths.add(invoice.getAtt());
                            }
                            if (StringUtils.isNotEmpty(invoice.getAttName())) {
                                fileNames.add(invoice.getAttName());
                            }
                        }
                    }
                }

                // 设置发票文件路径和名称(用逗号连接)
                mmisAsetStorageVo.setInvoiceAtt(String.join(",", filePaths));
                mmisAsetStorageVo.setInvoiceAttName(String.join(",", fileNames));
            }
        }

        return mmisAsetStorageVos;
    }

    @Override
    public MmisAsetStorageVo queryDocmentNum(MmisAsetStorageDto dto) {
        MmisAsetStorageVo mmisAsetStorageVo = mmisAsetStorageReadMapper.queryDocNum(dto);
        // 需拼接入出库标识，这里是入库：RK
        StringBuilder stb = new StringBuilder("RK");
        if (mmisAsetStorageVo == null) {
            String dateStr = DateUtil.getCurrentTime("yyyyMM");
            StringBuilder firstStr = stb.append(dateStr).append("0001");
            mmisAsetStorageVo.setDocmentNum(firstStr.toString());
        } else {
            StringBuilder append = stb.append(mmisAsetStorageVo.getDocmentNum());
            mmisAsetStorageVo.setDocmentNum(append.toString());
        }
        return mmisAsetStorageVo;
    }

    /**
     * 单据号查询
     *
     * @param dto
     * @return
     */
    @Override
    public MmisAsetStorageVo queryDocmentNumStr(MmisAsetStorageDto dto) {
        MmisAsetStorageVo mmisAsetStorageVo = mmisAsetStorageReadMapper.queryDocNum1();
        // 需拼接入出库标识，这里是入库：RK
        StringBuilder stb = new StringBuilder("RK");
        // RK+单据号 : RK + yyyyMM + "4位数字，代表第几份"
        StringBuilder append = stb.append(mmisAsetStorageVo.getDocmentNum());
        mmisAsetStorageVo.setDocmentNum(append.toString());
        return mmisAsetStorageVo;
    }

    /**
     * 查询app审核详情
     *
     * @param dto
     * @return
     */
    @Override
    public MmisAsetStorageVo queryAppAuditDetail(MmisAsetStorageDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<MmisAuditRcdfmVo> auditRcdfmVos = auditRcdfmReadService.getAuditDetails(dto.getAuditBchno(),
                dto.getSysUser().getHrmUser().getEmpCode());
        MmisAsetStorageVo asetStorageVo = mmisAsetStorageReadMapper.selectOneByBchno(dto.getAuditBchno());
        List<MmisAsetStorageDetailVo> storageDetailVos = storageDetailReadMapper
                .queryListByApplyId(asetStorageVo.getId());
        asetStorageVo.setTableDetails(storageDetailVos);
        if (CollectionUtil.isEmpty(auditRcdfmVos)) {
            auditRcdfmVos.sort(Comparator.comparing(MmisAuditRcdfmVo::getChkSeq));
            asetStorageVo.setAuditDetails(auditRcdfmVos);
        }
        return asetStorageVo;
    }

    @Override
    public List<MmisAsetStorageDetailVo> hasStroagedMats(MmisAsetStorageDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisAsetStorageReadMapper.queryHasStoragedMats(dto);
    }

    @Override
    public List<MmisAsetStorageDetailVo> hasStroagedMatsInApply(MmisAsetStorageDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisAsetStorageReadMapper.queryHasStoragedMatsInApply(dto);
    }

    /**
     * 查询库存物资信息，使用matUniqueCode作为key返回
     *
     * @param dto 查询参数
     * @return 物资库存列表
     */
    @Override
    public List<MmisAsetStorageDetailVo> hasStroagedMatsInApplyByUniqueCode(MmisAsetStorageDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisAsetStorageReadMapper.queryHasStoragedMatsInApplyByUniqueCode(dto);
    }

    /**
     * 根据matuniquecode查入库记录，或者根据item_num来查这个名字的入库记录
     * 
     * @param dto
     * @return
     */
    @Override
    public List<MmisAsetStorageDetailVo> queryStorageRecordsByMat2Code(MmisAsetStorageDto dto) {
        return mmisAsetStorageReadMapper.queryStorageRecordsByMat2Code(dto);
    }

    /**
     * 查询待结账的物资入库单（实际就是查没有期号的已经入库成功的入库单）
     * 
     * @param dto
     * @return
     */
    @Override
    public List<MmisAsetStorageVo> queryWaitAccountingReceipt(MmisAsetStorageDto dto) {
        try {
            // 调用Mapper的queryPendingStorageByPeriod方法查询待结账记录
            // 设置查询条件：settle_period_num IS NULL
            List<MmisAsetStorageVo> mmisAsetStorageVos = mmisAsetStorageReadMapper.queryPendingStorageByPeriod(dto);
            
            // 遍历集合，给每条数据查询入库详情得到品名name * 入库数量num ，把字符串给invtsummary
            for (MmisAsetStorageVo mmisAsetStorageVo : mmisAsetStorageVos) {
                // 查询入库详情
                List<MmisAsetStorageDetailVo> storageDetailVos = storageDetailReadMapper
                        .queryListByApplyId(mmisAsetStorageVo.getId());
                StringBuilder invtSummary = new StringBuilder();

                for (MmisAsetStorageDetailVo storageDetailVo : storageDetailVos) {
                    // 拼接品名和数量，品名*数量 为一对，上下两对之间用;拼接
                    invtSummary.append(storageDetailVo.getName()).append("*").append(storageDetailVo.getNum()).append(";");
                }
                // 把结果给mmisAsetStorageVo的invtsummary
                mmisAsetStorageVo.setInvtSummary(invtSummary.toString());
            }
            
            return mmisAsetStorageVos;
        } catch (Exception e) {
            log.error("查询待结账入库记录失败", e);
            throw new RuntimeException("查询待结账入库记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定时间段内待结账的入库记录（用于结账预览）
     *
     * @param dto 查询参数，包含startDate、endDate等
     * @return 入库记录列表及汇总数据
     */
    @Override
    public List<MmisAsetStorageVo> queryPendingStorageByPeriod(MmisAsetStorageDto dto) {
        if (dto == null) {
            return new ArrayList<>();
        }
        
        // 设置查询条件：待结账状态
        dto.setQuerySettle("1"); // 这个标志用于触发settle_period_num is null的条件
        
        List<MmisAsetStorageVo> results = mmisAsetStorageReadMapper.queryPendingStorageByPeriod(dto);
        
        if (CollectionUtil.isNotEmpty(results)) {
            // 处理入库摘要信息
            for (MmisAsetStorageVo vo : results) {
                if (vo.getId() != null) {
                    // 查询并设置入库摘要
                    List<MmisAsetStorageDetailVo> details = storageDetailReadMapper.queryListByApplyId(vo.getId());
                    if (CollectionUtil.isNotEmpty(details)) {
                        StringBuilder summary = new StringBuilder();
                        for (MmisAsetStorageDetailVo detail : details) {
                            if (summary.length() > 0) {
                                summary.append(";");
                            }
                            summary.append(detail.getName()).append("*").append(detail.getNum());
                        }
                        vo.setInvtSummary(summary.toString());
                    }
                }
            }
        }
        
        return results;
    }

    /**
     * 查询入库明细数据
     *
     * @param dto 查询参数
     * @return 入库明细列表
     */
    @Override
    public List<MmisAsetStorageDetailVo> queryStorageDetailList(MmisAsetStorageDto dto) {
        // 如果需要分页，可以添加分页代码

        return mmisAsetStorageReadMapper.queryStorageDetailList(dto);
    }

    /**
     * 信息科入库明细查询
     *
     * @param dto 查询参数
     * @return 信息科入库明细列表
     */
    @Override
    public List<MmisAsetStorageDetailVo> queryXinxiStorageDetailList(MmisAsetStorageDto dto) {
        return mmisAsetStorageReadMapper.queryXinxiStorageDetailList(dto);
    }
}
