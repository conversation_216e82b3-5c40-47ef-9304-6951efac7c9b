<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :query-form="queryForm"
    :queryMethod="queryXinxiStorageDetailList"
    show-export
    :export-config="exportConfig"
    name="信息科入库明细"
    :single-line="false"
    :bordered="false"
    :show-operation-button="false"
    @query-complete="updateTotalStats"
    :paging="false"
    class="receipt-detail-table"
    striped
    :row-props="rowProps"
  >
    <template #extendRightHeader>
      <n-card size="small" class="summary-card">
        <n-grid :cols="3" :x-gap="12">
          <!-- 第一行标签 -->
          <n-grid-item>
            <n-text>入库单数</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>入库物资数量</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>入库金额汇总</n-text>
          </n-grid-item>

          <!-- 第二行数值 -->
          <n-grid-item>
            <n-number-animation :from="0" :to="totalDocs" />
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalCount" />
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
        </n-grid>
      </n-card>
    </template>

    <template #extendFormItems>
      <n-form-item label="入库日期" path="dateRange">
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          :shortcuts="dateShortcuts"
          @update:value="handleDateRangeChange"
        />
      </n-form-item>
      <n-form-item label="单据号">
        <n-input v-model:value="queryForm.manualDocNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="物资名称">
        <n-input v-model:value="queryForm.name" :clearable="true" placeholder="支持模糊查询" />
      </n-form-item>
      <n-form-item label="物资代码">
        <n-input v-model:value="queryForm.itemNum" :clearable="true" />
      </n-form-item>
      <n-form-item label="信息科分类">
        <n-select
          v-model:value="queryForm.itemTypeCode"
          :options="xinxiTypeOptions"
          placeholder="请选择信息科分类"
          clearable
        />
      </n-form-item>
    </template>

    <template #extendButtons>
      <n-button type="primary" @click="handleQuery">查询</n-button>
      <n-button @click="handleReset">重置</n-button>
    </template>

    <template #contentTop>
      <div class="summary">
        <div class="title">信息科库房入库明细 - 日期: {{ formatDate(queryForm.startTime) }} 到: {{ formatDate(queryForm.endTime) }}</div>
        <div class="summary-info">
          <span>入库金额: {{ totalAmount.toFixed(2) }}</span>
          <span>入库合计: {{ totalCount }}</span>
          <span>低值易耗品: {{ lowValueCount }}</span>
          <span>耗材: {{ consumableCount }}</span>
          <span>配件: {{ accessoryCount }}</span>
        </div>
      </div>
    </template>
  </j-crud>
</template>