package com.jp.med.mmis.modules.matSum.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.MapUtil;
import com.jp.med.mmis.modules.asetType.mapper.read.MmisAsetTypeReadMapper;
import com.jp.med.mmis.modules.matApply.mapper.read.MmisMaterialApplyReadMapper;
import com.jp.med.mmis.modules.matReceipt.mapper.read.MmisAsetStorageReadMapper;
import com.jp.med.mmis.modules.outBound.mapper.read.MmisOutboundApplyReadMapper;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jp.med.mmis.modules.matSum.mapper.read.MmisMaterialSumReadMapper;
import com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumCheckVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumMonthlyTheoreticalVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumDetailVO;
import com.jp.med.mmis.modules.matSum.service.read.MmisMaterialSumReadService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.StringUtils;

import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisDetailVo;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisSummaryVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Transactional(readOnly = true)
@Service
public class MmisMaterialSumReadServiceImpl extends ServiceImpl<MmisMaterialSumReadMapper, MmisMaterialSumDto>
        implements MmisMaterialSumReadService {

    private static final Logger log = LoggerFactory.getLogger(MmisMaterialSumReadServiceImpl.class);

    @Autowired
    private MmisMaterialSumReadMapper mmisMaterialSumReadMapper;

    @Autowired
    private MmisAsetTypeReadMapper mmisAsetTypeReadMapper;

    // 物资申领
    @Autowired
    private MmisMaterialApplyReadMapper mmisMaterialApplyReadMapper;

    // 物资入库
    @Autowired
    private MmisAsetStorageReadMapper mmisAsetStorageReadMapper;

    // 物资出库
    @Autowired
    private MmisOutboundApplyReadMapper mmisOutboundApplyReadMapper;

    @Override
    public List<MmisMaterialSumVo> queryList(MmisMaterialSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialSumReadMapper.queryList(dto);
    }

    @Override
    public List<MmisMaterialSumVo> listSearch(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.listSearch(dto);
    }

    /**
     * 物资入库汇总
     * 
     * @param dto
     * @return
     */

    @Override
    public List<MmisMaterialSumVo> queryMatReceiptSum(MmisMaterialSumDto dto) {
        // PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialSumReadMapper.queryMatReceiptSum(dto);
    }

    /**
     * 信息科库房入库汇总
     *
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialSumVo> queryCustomReceiptSum(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.queryCustomReceiptSum(dto);
    }

    /**
     * 信息科库房出库汇总
     *
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialSumVo> queryCustomOutSum(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.queryCustomOutSum(dto);
    }

    // 汇总一行的总金额
    public HashMap<String, BigDecimal> sumTotalRowAmtByParentCode(List<MmisMaterialSumVo> sumVos) {
        HashMap<String, BigDecimal> map = new HashMap<>();
        for (MmisMaterialSumVo sumVo : sumVos) {
            // 获取当前SumVo的parent_code
            String parentCode = sumVo.getParentCode();
            // 获取当前SumVo的totalRowAmt
            BigDecimal totalRowAmt = sumVo.getTotalAmt();

            // 如果map中已经有了这个parent_code，那么就累加totalRowAmt
            if (map.containsKey(parentCode)) {
                map.put(parentCode, map.get(parentCode).add(totalRowAmt));
            } else {
                // 如果map中没有这个parent_code，就初始化这个键值对
                map.put(parentCode, totalRowAmt);
            }
        }
        return map;
    }

    /**
     * 物资出库汇总
     * 
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialSumVo> queryMatOutSum(MmisMaterialSumDto dto) {
        // PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialSumReadMapper.queryMatOutSum(dto);
    }

    /**
     * 低值易耗品汇总(加上高低值查询的)
     * 
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialSumVo> queryMatLowPriceSum(MmisMaterialSumDto dto) {
        // PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialSumReadMapper.queryMatLowPriceSum(dto);
    }

    /**
     * 物资类目汇总账
     * 
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialSumVo> queryMatCatalogSum(MmisMaterialSumDto dto) {
        // PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialSumReadMapper.queryMatCatalogSum(dto);
    }

    /**
     * 查询工作台上面的申领，入库，出库，库存的本月总金额，和数量
     * 
     * @param dto
     * @return
     */
    @Override
    public MmisMaterialSumVo queryAllAmtNumInMyCtrl(MmisMaterialSumDto dto) {
        MmisMaterialSumVo result = new MmisMaterialSumVo();

        // 获取当月第一天和最后一天
        String currentDate = DateUtil.getCurrentTime("yyyy-MM-dd");
        dto.setStartTime(currentDate.substring(0, 8) + "01"); // yyyy-MM-01
        dto.setEndTime(currentDate.substring(0, 8) + "31"); // yyyy-MM-31

        // 1. 查询本月入库总金额和数量
        MmisMaterialSumVo storageSum = mmisMaterialSumReadMapper.queryStorageAmtNum(dto);
        if (storageSum != null) {
            result.setStorageAmt(storageSum.getStorageAmt()); // 入库总金额
            result.setStorageNum(storageSum.getStorageNum()); // 入库总数量
        }

        // 2. 查询本月出库总金额和数量
        MmisMaterialSumVo outboundSum = mmisMaterialSumReadMapper.queryOutboundAmtNum(dto);
        if (outboundSum != null) {
            result.setOutboundAmt(outboundSum.getOutboundAmt()); // 出库总金额
            result.setOutboundNum(outboundSum.getOutboundNum()); // 出库总数量
        }

        // 3. 查询本月申领总金额和数量
        MmisMaterialSumVo applySum = mmisMaterialSumReadMapper.queryApplyAmtNum(dto);
        if (applySum != null) {
            result.setApplyAmt(applySum.getApplyAmt()); // 申领总金额
            result.setApplyNum(applySum.getApplyNum()); // 申领总数量
        }

        // 4. 查询当前库存总金额和数量
        MmisMaterialSumVo stockSum = mmisMaterialSumReadMapper.queryStockAmtNum(dto);
        if (stockSum != null) {
            result.setStockAmt(stockSum.getStockAmt()); // 库存总金额
            result.setStockNum(stockSum.getStockNum()); // 库存总数量
        }

        return result;
    }

    @Override
    public List<MmisMaterialSumVo> lessThanReplenishThreshold(MmisMaterialSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 查询库存预警信息
        List<MmisMaterialSumVo> warningList = mmisMaterialSumReadMapper.queryStockWarning(dto);

        if (CollectionUtil.isEmpty(warningList)) {
            return warningList;
        }

        // 统计各种预警状态的数量
        // 告急数量
        int alertCount = 0;
        // 紧急数量
        int urgentCount = 0;
        // 补货数量
        int replenishCount = 0;

        for (MmisMaterialSumVo item : warningList) {
            // 当前库存
            BigDecimal stockNum = item.getStockNum();
            // 补货阈值
            BigDecimal replenishNum = item.getReplenishNum();

            if (stockNum != null && replenishNum != null && replenishNum.compareTo(BigDecimal.ZERO) > 0) {
                // 计算库存和补货阈值的比率
                BigDecimal ratio = stockNum.divide(replenishNum, 2, RoundingMode.HALF_UP);

                // 根据比率判断预警级别
                if (ratio.compareTo(new BigDecimal("0.2")) <= 0) {
                    // 库存低于补货阈值20%，告急
                    alertCount++;
                    item.setWarningStatus("1"); // 告急
                } else if (ratio.compareTo(new BigDecimal("0.5")) <= 0) {
                    // 库存低于补货阈值50%，紧急
                    urgentCount++;
                    item.setWarningStatus("2"); // 紧急
                } else if (ratio.compareTo(new BigDecimal("1")) <= 0) {
                    // 库存低于补货阈值，需补货
                    replenishCount++;
                    item.setWarningStatus("3"); // 补货
                }
            }
        }

        return warningList;
    }

    /**
     * 查询物资分类金额占比
     */
    @Override
    public List<MmisMaterialSumVo> queryMatTypeAmtRatio(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.queryMatTypeAmtRatio(dto);
    }

    @Override
    public List<MmisMaterialSumCheckVO> queryStockCheck(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.queryStockCheck(dto);
    }

    /**
     * 查询月度理论金额数据
     * 从2024年12月到当前月份的理论金额、入库金额、出库金额
     */
    @Override
    public List<MmisMaterialSumMonthlyTheoreticalVO> queryMonthlyTheoreticalAmount(MmisMaterialSumDto dto) {
        return mmisMaterialSumReadMapper.queryMonthlyTheoreticalAmount(dto);
    }

    /**
     * 查询物资对账差异详情
     * 
     * @param dto 包含物资唯一编码的查询参数
     * @return 物资对账差异详情结果列表
     */
    @Override
    public List<MmisMaterialSumDetailVO> queryStockCheckDetail(MmisMaterialSumDto dto) {
        // 调用Mapper方法查询对账差异详情
        return mmisMaterialSumReadMapper.queryStockCheckDetail(dto);
    }

    /**
     * 查询指定物资的库存差异分析信息
     * 
     * @param dto 包含物资唯一编码的参数对象
     * @return 包含库存差异分析的Map对象
     */
    @Override
    public Map<String, Object> queryStockAnalysis(MmisMaterialSumDto dto) {
        Map<String, Object> result = new HashMap<>();

        // 检查参数是否有效
        if (dto == null || StringUtils.isEmpty(dto.getMatUniqueCode())) {
            // 返回默认值
            return getDefaultAnalysisResult();
        }

        // 查询物资详细信息
        List<MmisMaterialSumDetailVO> detailList = mmisMaterialSumReadMapper.queryStockCheckDetail(dto);

        if (detailList == null || detailList.isEmpty()) {
            // 未找到数据，返回默认值
            return getDefaultAnalysisResult();
        }

        // 取第一条数据
        MmisMaterialSumDetailVO detail = detailList.get(0);

        // 构建返回结果
        result.put("initialNum", detail.getInitialNum() != null ? detail.getInitialNum() : BigDecimal.ZERO);
        result.put("initialAmt", detail.getInitialAmt() != null ? detail.getInitialAmt() : BigDecimal.ZERO);
        result.put("storageNum", detail.getStorageNum() != null ? detail.getStorageNum() : BigDecimal.ZERO);
        result.put("storageAmt", detail.getStorageAmt() != null ? detail.getStorageAmt() : BigDecimal.ZERO);
        result.put("storageCount", detail.getStorageCount() != null ? detail.getStorageCount() : 0);
        result.put("outboundNum", detail.getOutboundNum() != null ? detail.getOutboundNum() : BigDecimal.ZERO);
        result.put("outboundAmt", detail.getOutboundAmt() != null ? detail.getOutboundAmt() : BigDecimal.ZERO);
        result.put("outboundCount", detail.getOutboundCount() != null ? detail.getOutboundCount() : 0);
        result.put("theoreticalNum", detail.getTheoreticalNum() != null ? detail.getTheoreticalNum() : BigDecimal.ZERO);
        result.put("theoreticalAmt", detail.getTheoreticalAmt() != null ? detail.getTheoreticalAmt() : BigDecimal.ZERO);
        result.put("actualNum", detail.getActualNum() != null ? detail.getActualNum() : BigDecimal.ZERO);
        result.put("actualAmt", detail.getActualAmt() != null ? detail.getActualAmt() : BigDecimal.ZERO);
        result.put("diffNum", detail.getDiffNum() != null ? detail.getDiffNum() : BigDecimal.ZERO);
        result.put("diffAmt", detail.getDiffAmt() != null ? detail.getDiffAmt() : BigDecimal.ZERO);
        result.put("diffStatus", detail.getDiffStatus() != null ? detail.getDiffStatus() : "NoDeviation");

        return result;
    }

    /**
     * 获取默认的分析结果
     * 
     * @return 包含默认值的Map对象
     */
    private Map<String, Object> getDefaultAnalysisResult() {
        Map<String, Object> defaultResult = new HashMap<>();
        defaultResult.put("initialNum", BigDecimal.ZERO);
        defaultResult.put("initialAmt", BigDecimal.ZERO);
        defaultResult.put("storageNum", BigDecimal.ZERO);
        defaultResult.put("storageAmt", BigDecimal.ZERO);
        defaultResult.put("storageCount", 0);
        defaultResult.put("outboundNum", BigDecimal.ZERO);
        defaultResult.put("outboundAmt", BigDecimal.ZERO);
        defaultResult.put("outboundCount", 0);
        defaultResult.put("theoreticalNum", BigDecimal.ZERO);
        defaultResult.put("theoreticalAmt", BigDecimal.ZERO);
        defaultResult.put("actualNum", BigDecimal.ZERO);
        defaultResult.put("actualAmt", BigDecimal.ZERO);
        defaultResult.put("diffNum", BigDecimal.ZERO);
        defaultResult.put("diffAmt", BigDecimal.ZERO);
        defaultResult.put("diffStatus", "NoDeviation");
        return defaultResult;
    }

    /**
     * 查询精度转换分析详情
     * 
     * @param dto 查询参数
     * @return 精度分析详情列表
     */
    @Override
    public List<PrecisionAnalysisDetailVo> queryPrecisionAnalysisDetail(MmisMaterialSumDto dto) {
        if (dto.getPrecision() == null) {
            throw new IllegalArgumentException("精度参数不能为空");
        }
        if (dto.getPrecision() <= 0) {
            throw new IllegalArgumentException("精度参数必须为正整数");
        }
        return mmisMaterialSumReadMapper.queryPrecisionAnalysisDetail(dto);
    }

    /**
     * 查询精度转换分析汇总
     * 
     * @param dto 查询参数
     * @return 精度分析汇总信息
     */
    @Override
    public PrecisionAnalysisSummaryVo queryPrecisionAnalysisSummary(MmisMaterialSumDto dto) {
        if (dto.getPrecision() == null) {
            throw new IllegalArgumentException("精度参数不能为空");
        }
        if (dto.getPrecision() <= 0) {
            throw new IllegalArgumentException("精度参数必须为正整数");
        }
        return mmisMaterialSumReadMapper.queryPrecisionAnalysisSummary(dto);
    }

    /**
     * 根据物资唯一码查询物资信息
     * 
     * @param matUniqueCode 物资唯一码
     * @return 物资汇总信息
     */
    @Override
    public MmisMaterialSumDto selectByMatUniqueCode(String matUniqueCode) {
        if (StringUtils.isEmpty(matUniqueCode)) {
            return null;
        }
        
        try {
            // 使用MyBatis-Plus的方法查询 - 修复类型推断问题
            LambdaQueryWrapper<MmisMaterialSumDto> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MmisMaterialSumDto::getMatUniqueCode, matUniqueCode);
            
            List<MmisMaterialSumDto> list = baseMapper.selectList(wrapper);
            return list.isEmpty() ? null : list.get(0);
            
        } catch (Exception e) {
            // 如果出现异常，记录日志并返回null
            log.error("查询物资失败，唯一码: {}, 错误: {}", matUniqueCode, e.getMessage());
            return null;
        }
    }

    /**
     * 数据完整性检查
     * 检查assist表中use_status='1'的数据在info表和sum表的存在情况
     * 
     * @return 数据完整性检查结果列表
     */
    @Override
    public List<Map<String, Object>> checkDataIntegrity() {
        try {
            log.info("开始执行数据完整性检查");
            List<Map<String, Object>> result = mmisMaterialSumReadMapper.checkDataIntegrity();
            log.info("数据完整性检查完成，共检查{}条数据", result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("数据完整性检查执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据完整性检查执行失败: " + e.getMessage());
        }
    }
}
