<template>
  <j-crud
    :queryMethod="queryCustomReceiptSum"
    :columns="columns"
    show-export
    :export-config="exportConfig"
    ref="crudRef"
    :queryForm="queryForm"
    name="信息科库房入库汇总"
    :show-operation-button="false"
    :paging="false"
    :defaultOpenAccurateQuery="true"
    @query-complete="updateTotalStats"
    :single-line="false"
    :bordered="false"
  >
    <template #extendRightHeader>
      <n-card size="small" class="summary-card">
        <n-grid :cols="4" :x-gap="12">
          <!-- 第一行标签 -->
          <n-grid-item>
            <n-text>合计</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>低值易耗品</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>耗材</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>配件</n-text>
          </n-grid-item>

          <!-- 第二行完整精度 -->
          <n-grid-item>
            <n-number-animation :from="0" :to="totalAmount" :precision="6">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="lowValueAmount" :precision="6">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="consumableAmount" :precision="6">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="accessoryAmount" :precision="6">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>

          <!-- 第三行四舍五入到2位小数 -->
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="totalAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="lowValueAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="consumableAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="accessoryAmount" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
        </n-grid>
      </n-card>
    </template>
    <template #extendFormItems>
      <n-form-item label="日期范围">
        <n-date-picker
          v-model:formatted-value="createTimeRange"
          value-format="yyyy-MM-dd"
          @update-formatted-value="createDateChange"
          :update-value-on-close="true"
          type="daterange"
          :clearable="true"
          :shortcuts="rangeShortcuts"
        ></n-date-picker>
      </n-form-item>
      <n-form-item label="部门">
        <j-bus-hos-org v-model:value="queryForm.orgID" :clearable="true"></j-bus-hos-org>
      </n-form-item>
      <n-form-item label="物资类别">
        <n-tree-select
          v-model:value="queryForm.asetType"
          :options="allAsetType"
          key-field="code"
          label-field="name"
          :clearable="true"
        ></n-tree-select>
      </n-form-item>
      <n-form-item label="入库类型">
        <n-select v-model:value="queryForm.invType" :options="allInvType" :clearable="true"></n-select>
      </n-form-item>
    </template>
  </j-crud>
</template>
<script lang="ts" setup>
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import { onMounted, ref, watch } from 'vue'
  import { queryCustomReceiptSum } from '@/api/mmis/matSum/MaterialSumWeb'
  import { queryMmisAsetType } from '@/api/mmis/asetType/asetTypeWeb'
  import { queryMmisInvTypeCfgList } from '@/api/mmis/useCodeCfg/InvTypeCfgWeb'
  import { ExcelType } from '@/utils/excel'

  // data
  const crudRef = ref()
  let queryForm = ref({
    orgID: '',
    asetType: '',
    invType: '',
    startTime: '',
    endTime: '',
  })

  //入库日期范围change
  let createTimeRange = ref<any>()
  const createDateChange = (value: [string, string] | null) => {
    if (value != null) {
      queryForm.value.startTime = value[0]
      queryForm.value.endTime = value[1]
    } else {
      queryForm.value.startTime = ''
      queryForm.value.endTime = ''
    }
  }

  const rangeShortcuts = {
    昨年: () => {
      return getLastYear()
    },
    上季: () => {
      return getLastQuarter()
    },
    上月: () => {
      return getLastMonth()
    },
    上周: () => {
      // 获取当前日期
      let today = new Date()
      // 获取当前是星期几
      let day = today.getDay()
      let startDate, endDate
      if (day == 0) {
        // 计算本周第一天的日期
        startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 13)
        // 计算本周最后一天的日期
        endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7)
      } else {
        startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day - 6)
        endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day)
      }
      return [startDate.getTime(), endDate.getTime()]
    },
    本周: () => {
      return getCurrentWeek()
    },
    本月: () => {
      return getCurrentMonth()
    },
    本年: () => {
      return getCurrentYear()
    },
    本季: () => {
      return getCurrentQuarter()
    },
  }

  const getLastMonth = () => {
    // 获取当前日期
    let now = new Date()
    // 获取当前年份
    let year = now.getFullYear()
    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth()
    // 获取上一个月的年份和月份
    if (month === 0) {
      year--
      month = 12
    }
    // 计算上个月的第一天
    let startDate = new Date(year, month - 1, 1)
    // 计算上个月的最后一天
    let endDate = new Date(year, month, 0)
    // 返回时间戳数组
    return [startDate.getTime(), endDate.getTime()]
  }

  //获取上一个季度
  const getLastQuarter = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 确定上一个季度
    let lastQuarter = Math.ceil(month / 3) - 1

    // 如果当前季度是1，则上一个季度是去年第4季度
    if (lastQuarter === 0) {
      lastQuarter = 4
      year -= 1
    }

    // 计算上一个季度第一个月的开始日期
    let firstMonthOfLastQuarter = (lastQuarter - 1) * 3 + 1
    let firstDayOfLastQuarter = new Date(year, firstMonthOfLastQuarter - 1, 1)

    // 计算上一个季度最后一个月的结束日期
    let lastMonthOfLastQuarter = lastQuarter * 3
    let lastDayOfLastQuarter = new Date(year, lastMonthOfLastQuarter, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfLastQuarter.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfLastQuarter.getTime()
    let endDateTimestamp = lastDayOfLastQuarter.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }

  //获取当前季度
  const getCurrentQuarter = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 确定当前季度
    let quarter = Math.ceil(month / 3)

    // 计算当前季度第一个月的开始日期
    let firstMonthOfQuarter = (quarter - 1) * 3 + 1
    let firstDayOfQuarter = new Date(year, firstMonthOfQuarter - 1, 1)

    // 计算当前季度最后一个月的结束日期
    let lastMonthOfQuarter = quarter * 3
    let lastDayOfQuarter = new Date(year, lastMonthOfQuarter, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfQuarter.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfQuarter.getTime()
    let endDateTimestamp = lastDayOfQuarter.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }

  //获取今年开始/结束时间戳
  const getCurrentYear = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 计算当前年份的第一天
    let firstDayOfYear = new Date(year, 0, 1)

    // 计算当前年份的最后一天
    // 下一年度的第一天减去1毫秒就是当前年份的最后一天
    let lastDayOfYear = new Date(year + 1, 0, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfYear.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfYear.getTime()
    let endDateTimestamp = lastDayOfYear.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }

  //获取昨年开始/结束时间戳
  const getLastYear = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let currentYear = now.getFullYear()

    // 计算去年的年份
    let lastYear = currentYear - 1

    // 计算去年第一天
    let firstDayOfLastYear = new Date(lastYear, 0, 1)

    // 计算去年最后一天
    let lastDayOfLastYear = new Date(lastYear, 11, 31)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfLastYear.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfLastYear.getTime()
    let endDateTimestamp = lastDayOfLastYear.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }

  //获取当前月份
  const getCurrentMonth = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 计算当前月份的第一天
    let startDate = new Date(year, month - 1, 1)

    // 计算当前月份的最后一天
    // 下一个月的第一天减去1毫秒就是当前月份的最后一天
    let endDate = new Date(year, month, 0)

    // 返回时间戳数组
    return [startDate.getTime(), endDate.getTime()]
  }

  // 获取当前周
  const getCurrentWeek = () => {
    // 获取当前日期
    let today = new Date()
    // 获取当前是星期几
    let day = today.getDay()
    let startDate,
      endDate = null
    if (day == 0) {
      // 计算本周第一天的日期
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day - 6)
      // 计算本周最后一天的日期
      endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day)
    } else {
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 1)
      endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 7)
    }
    return [startDate.getTime(), endDate.getTime()]
  }

  const columns = ref<CRUDColumnInterface[]>([
    { title: '#', key: 'index', width: 50 },
    { title: '部门代码', key: 'inOrgId', width: 100 },
    { title: '部门名称', key: 'inOrgName', width: 100 },
    { title: '仓库代码', key: 'wrhsCode', width: 100 },
    { title: '仓库名称', key: 'wrhsName', width: 100 },
    { title: '合计', key: 'totalAmt', width: 100, summary: true },
    { title: '低值易耗品', key: 'lowPriceAmt', width: 100, summary: true },
    { title: '耗材', key: 'consumableAmt', width: 100, summary: true },
    { title: '配件', key: 'accessoryAmt', width: 100, summary: true },
  ])

  //所有物资目录(树形数据)
  let allAsetType = ref<Array<any>>([])
  //查询所有物资目录
  const queryAllAsetType = () => {
    queryMmisAsetType({}).then(res => {
      allAsetType.value = res.data
    })
  }

  //入出库类型 ->这里只要入库类型
  let allInvType = ref<Array<any>>([])
  const queryInvTypeCfg = () => {
    queryMmisInvTypeCfgList({}).then(res => {
      //这里的选项框只要入库类型
      let filterData = res.data.filter((d: any) => d.typeCode.startsWith('R'))
      filterData.forEach((item: any) => {
        allInvType.value.push({
          label: item.name,
          value: item.typeCode,
        })
      })
    })
  }

  onMounted(() => {
    queryInvTypeCfg()
    queryAllAsetType()
  })

  const a = new Date().getTime() //获取到当前时间戳
  const b = new Date(a) //创建一个指定的日期对象
  const exportName = (now: any) => {
    const year = now.getFullYear() //年份
    const month = now.getMonth() + 1 //月份（0-11）
    const date = now.getDate() //天数（1到31）
    return '信息科库房入库汇总(' + year + '年' + month + '月' + date + '日)'
  }

  //导出excel
  let exportConfig = ref<ExcelType>({
    tableRef: crudRef,
    excelName: exportName(b),
    exportFunc: queryCustomReceiptSum,
    formData: () => queryForm,
    enableSummary: true,
  })

  let totalAmount = ref(0)
  let lowValueAmount = ref(0)
  let consumableAmount = ref(0)
  let accessoryAmount = ref(0)

  const updateTotalStats = () => {
    if (!crudRef.value?.originData) return

    totalAmount.value = 0
    lowValueAmount.value = 0
    consumableAmount.value = 0
    accessoryAmount.value = 0

    crudRef.value.originData.forEach((item: any) => {
      totalAmount.value += item.totalAmt || 0
      lowValueAmount.value += item.lowPriceAmt || 0
      consumableAmount.value += item.consumableAmt || 0
      accessoryAmount.value += item.accessoryAmt || 0
    })
  }

  watch(
    () => crudRef.value?.originData,
    () => {
      updateTotalStats()
    },
    { deep: true }
  )
</script>

<script lang="ts">
  export default {
    name: 'index',
  }
</script>