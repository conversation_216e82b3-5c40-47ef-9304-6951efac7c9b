package com.jp.med.mmis.modules.matSum.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto;
import com.jp.med.mmis.modules.matSum.dto.AdjustStockDTO;
import com.jp.med.mmis.modules.matSum.dto.PrecisionConversionDto;
import com.jp.med.mmis.modules.matSum.service.read.MmisMaterialSumReadService;
import com.jp.med.mmis.modules.matSum.service.write.MmisMaterialSumWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
@Api(value = "物资汇总表", tags = "物资汇总表")
@RestController
@RequestMapping("mmisMaterialSum")
public class MmisMaterialSumController {

    @Autowired
    private MmisMaterialSumReadService mmisMaterialSumReadService;

    @Autowired
    private MmisMaterialSumWriteService mmisMaterialSumWriteService;

    /**
     * 列表 ：这个是给物资申请用的，所以查询汇总的时候没有分仓库(库位代码分别汇总)
     */
    @ApiOperation("查询物资汇总表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.paging(mmisMaterialSumReadService.queryList(dto));
    }

    /**
     * 列表 ：这个是物资汇总界面用的，可以加上库位代码分
     */
    @ApiOperation("查询物资汇总表")
    @PostMapping("/queryList")
    public CommonResult<?> searchList(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.listSearch(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增物资汇总表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody MmisMaterialSumDto dto) {
        mmisMaterialSumWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改物资汇总表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisMaterialSumDto dto) {
        mmisMaterialSumWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除物资汇总表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisMaterialSumDto dto) {
        mmisMaterialSumWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("入库物资汇总")
    @PostMapping("/queryMatReceiptSum")
    public CommonResult<?> queryMatReceiptSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMatReceiptSum(dto));
    }

    @ApiOperation("信息科库房入库汇总")
    @PostMapping("/queryCustomReceiptSum")
    public CommonResult<?> queryCustomReceiptSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryCustomReceiptSum(dto));
    }

    @ApiOperation("信息科库房出库汇总")
    @PostMapping("/queryCustomOutSum")
    public CommonResult<?> queryCustomOutSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryCustomOutSum(dto));
    }

    @ApiOperation("出库物资汇总")
    @PostMapping("/queryMatOutSum")
    public CommonResult<?> queryMatOutSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMatOutSum(dto));
    }

    @ApiOperation("低值易耗品汇总")
    @PostMapping("/queryMatLowPriceSum")
    public CommonResult<?> queryMatLowPriceSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMatLowPriceSum(dto));
    }

    @ApiOperation("物资类目汇总账")
    @PostMapping("/queryMatCatalogSum")
    public CommonResult<?> queryMatCatalogSum(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMatCatalogSum(dto));
    }

    @ApiOperation("工作台物资申领。入出库，库存金额与数量汇总")
    @PostMapping("/queryAllAmtNumInMyCtrl")
    public CommonResult<?> queryAllAmtNumInMyCtrl(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryAllAmtNumInMyCtrl(dto));
    }

    @ApiOperation("当前低于库存阈值的物资信息")
    @PostMapping("/lessThanReplenishThreshold")
    public CommonResult<?> lessThanReplenishThreshold(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.paging(mmisMaterialSumReadService.lessThanReplenishThreshold(dto));
    }

    @ApiOperation("查询物资分类金额占比")
    @PostMapping("/queryMatTypeAmtRatio")
    public CommonResult<?> queryMatTypeAmtRatio(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMatTypeAmtRatio(dto));
    }

    @ApiOperation("查询物资对账数据")
    @PostMapping("/queryStockCheck")
    public CommonResult<?> queryStockCheck(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryStockCheck(dto));
    }

    @ApiOperation("查询月度理论金额")
    @PostMapping("/queryMonthlyTheoreticalAmount")
    public CommonResult<?> queryMonthlyTheoreticalAmount(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryMonthlyTheoreticalAmount(dto));
    }

    /**
     * 查询物资对账差异详情
     */
    @ApiOperation("查询物资对账差异详情")
    @PostMapping("/queryStockCheckDetail")
    public CommonResult<?> queryStockCheckDetail(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryStockCheckDetail(dto));
    }

    /**
     * 查询物资库存差异分析
     */
    @ApiOperation("查询物资库存差异分析")
    @PostMapping("/queryStockAnalysis")
    public CommonResult<?> queryStockAnalysis(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryStockAnalysis(dto));
    }

    /**
     * 调整库存
     */
    @ApiOperation("调整库存")
    @PostMapping("/adjustStock")
    public CommonResult<?> adjustStock(@RequestBody AdjustStockDTO dto) {
        return CommonResult.success(mmisMaterialSumWriteService.adjustStock(dto));
    }

    /**
     * 查询精度转换分析详情
     */
    @ApiOperation("查询精度转换分析详情")
    @PostMapping("/queryPrecisionAnalysisDetail")
    public CommonResult<?> queryPrecisionAnalysisDetail(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryPrecisionAnalysisDetail(dto));
    }

    /**
     * 查询精度转换分析汇总
     */
    @ApiOperation("查询精度转换分析汇总")
    @PostMapping("/queryPrecisionAnalysisSummary")
    public CommonResult<?> queryPrecisionAnalysisSummary(@RequestBody MmisMaterialSumDto dto) {
        return CommonResult.success(mmisMaterialSumReadService.queryPrecisionAnalysisSummary(dto));
    }

    /**
     * 执行精度转换
     */
    @ApiOperation("执行精度转换")
    @PostMapping("/convertPrecision")
    public CommonResult<?> convertPrecision(@RequestBody PrecisionConversionDto dto) {
        // 参数校验
        if (dto.getPrecision() == null || dto.getPrecision() < 1 || dto.getPrecision() > 6) {
            return CommonResult.error("精度必须在1-6之间");
        }

        // 调用服务层执行精度转换
        return CommonResult.success(mmisMaterialSumWriteService.convertPrecision(dto));
    }

    /**
     * 基于分析详情一键执行精度转换
     */
    @ApiOperation("基于分析详情一键执行精度转换")
    @PostMapping("/batchConvertPrecisionByAnalysis")
    public CommonResult<?> batchConvertPrecisionByAnalysis(@RequestBody PrecisionConversionDto dto) {
        // 参数校验
        if (dto.getPrecision() == null || dto.getPrecision() < 1 || dto.getPrecision() > 6) {
            return CommonResult.error("精度必须在1-6之间");
        }

        // 调用服务层执行批量精度转换
        return CommonResult.success(mmisMaterialSumWriteService.batchConvertPrecisionByAnalysis(dto));
    }

    /**
     * 数据完整性检查
     * 检查assist表中use_status='1'的数据在info表和sum表的存在情况
     */
    @ApiOperation("数据完整性检查")
    @GetMapping("/checkDataIntegrity")
    public CommonResult<?> checkDataIntegrity() {
        return CommonResult.success(mmisMaterialSumReadService.checkDataIntegrity());
    }

}
