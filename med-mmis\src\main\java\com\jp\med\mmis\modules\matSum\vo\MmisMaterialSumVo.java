package com.jp.med.mmis.modules.matSum.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;
import java.util.List;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
@Data
public class MmisMaterialSumVo {

	/** id */
	private Integer id;

	/** 货号(物资代码) */
	private String itemNum;

	/** 库存数量 */
	private BigDecimal num;

	/** 运输成本 */
	private BigDecimal freightCost;

	/** 物资名称 */
	private String name;

	/** 规格 */
	private String modspec;

	/** 库位代码(仓库内的位置) */
	private String wrhsAddr;

	/** 仓库代码(几号仓库) */
	private String wrhsCode;

	/** 计量单位编码(对应常用代码中的计量方式:不是计量单位) */
	private String meterCode;

	/** 单价(类比物资信息中的参考进价) */
	private BigDecimal price;

	/** 金额 */
	private BigDecimal amt;

	/** 金额(适配老系统的) */
	private BigDecimal oldAmt;

	/** 每件细数(配合计量单位，如1张1 张的入库，1个..，默认为1.) */
	private Integer itemCount;

	/** 生产日期 */
	private String mfgDate;

	/** 到期日（和物资信息的那个同名字段不同，这里不是保质期，而是具体时间） */
	private String exprinDate;

	/** 物资品牌 */
	private String asetBrad;

	/** 来源单号 */
	private String sourceNum;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改人 */
	private String updtr;

	/** 修改时间 */
	private String updateTime;

	/** 逻辑删除标志 */
	private Integer isDeleted;

	/** 组织id */
	private String hospitalId;

	/**
	 * 对应物资辅助表的唯一详细信息，价格对应
	 */
	private String matUniqueCode;

	/**
	 * 实际数量
	 */
	private BigDecimal actNum;
	/** 计量方式名称 */
	private String meterUnitName;

	/**
	 * 生成的key
	 */
	private String key;

	/**
	 * 汇总金额，只是单行的（根据类目分类，就是说这是单个类目的总金额）
	 */
	private BigDecimal totalAmt;

	private BigDecimal lowPriceAmt;

	private BigDecimal otherAmt;

	private BigDecimal norEquAmt;

	private BigDecimal speEquAmt;
	/**
	 * 部门代码
	 */
	private String inOrgId;

	private String wrhsName;

	private String wrhsAddrName;

	private String inOrgName;

	private String parentCode;

	private String faTypeName;

	private String outOrgName;

	private String outOrgId;

	private BigDecimal swyp;
	private BigDecimal mzp;
	private BigDecimal dqwx;
	private BigDecimal fwwx;
	private BigDecimal frcl;
	private BigDecimal qcwx;
	private BigDecimal qjyp;
	private BigDecimal whyp;
	private BigDecimal ysp;
	private BigDecimal qtcl;
	private BigDecimal wjcl;
	private BigDecimal jj;
	private BigDecimal zysb1;

	/** 初始金额 */
	private BigDecimal initialAmt;
	private BigDecimal storageAmt;
	private BigDecimal outboundAmt;
	private BigDecimal applyAmt;
	private BigDecimal stockAmt;

	private BigDecimal storageNum;
	private BigDecimal outboundNum;
	private BigDecimal applyNum;
	private BigDecimal stockNum;

	private String warningStatus;
	private Integer alertCount;
	private Integer urgentCount;
	private Integer replenishCount;
	private List<MmisMaterialSumVo> warningList;
	private String replenishThreshold;
	private String urgentThreshold;
	private String alertThreshold;
	private BigDecimal replenishNum;

	/** 物资分类名称 (低值易耗品/其他材料/一般设备/专用设备) */
	private String typeName;

	/** 金额占比 (百分比) */
	private BigDecimal amtRatio;

	/** 信息科库房专用字段 - 耗材金额 */
	private BigDecimal consumableAmt;

	/** 信息科库房专用字段 - 配件金额 */
	private BigDecimal accessoryAmt;

}
