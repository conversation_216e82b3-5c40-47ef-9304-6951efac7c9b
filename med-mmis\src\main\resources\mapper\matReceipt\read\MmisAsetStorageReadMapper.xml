<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matReceipt.mapper.read.MmisAsetStorageReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo" id="asetStorageMap">
        <result property="id" column="id" />
        <result property="poNum" column="po_num" />
        <result property="receiptNum" column="receipt_num" />
        <result property="billDate" column="bill_date" />
        <result property="supplierName" column="supplier_name" />
        <result property="docmentNum" column="docment_num" />
        <result property="manualDocNum" column="manual_doc_num" />
        <result property="trackNum" column="track_num" />
        <result property="invoiceNum" column="invoice_num" />
        <result property="remark" column="remark" />
        <result property="appyOrgId" column="appy_org_id" />
        <result property="appyer" column="appyer" />
        <result property="wrhsCode" column="wrhs_code" />
        <result property="typeCode" column="type_code" />
        <result property="isDeleted" column="is_deleted" />
        <result property="hospitalId" column="hospital_id" />
        <result property="auditBchno" column="audit_bchno" />
        <result property="chkState" column="chk_state" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo">
        SELECT a.id as id, emp.emp_name as appyer, org.org_name as appyOrgId, a.appy_org_id as
        orgId, a.po_num as poNum, a.receipt_num as receiptNum, a.bill_date as billDate,
        a.supplier_name as supplierName, a.docment_num as docmentNum, a.manual_doc_num as
        manualDocNum, a.track_num as trackNum, a.invoice_num as invNum, a.remark as remark,
        a.wrhs_code as wrhsCode, a.type_code as typeCode, inv.name as invType, a.chk_state as
        chkState, a.audit_bchno as auditBchno, a.in_status as inStatus, a.in_emp as inEmp, a.in_time
        as inTime, a.in_remark as inRemark, a.crter as crter, a.create_time as createTime, case when
        position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as auditFlag,
        a.hospital_id as hospitalId, a.invo_id as invo_Id, a.reim_status_flag as reimStatusFlag,
        a.att as att, a.att_name as att_name,supplier_id as supplierId ,a.settle_period_num as
        settlePeriodNum FROM mmis_aset_storage a LEFT JOIN hrm_employee_info emp ON a.appyer =
        emp.emp_code LEFT JOIN hrm_org org ON a.appy_org_id = org.org_id LEFT JOIN mmis_inv_type_cfg
        inv ON a.type_code = inv.type_code LEFT JOIN mmis_invo_rcd invo ON a.invo_id = invo.id
        ::VARCHAR left join ( select a.bchno, a.chker from mmis_audit_rcdfm a inner join ( select
        e.bchno, min(e.chk_seq) as seq from mmis_audit_rcdfm e where e.chk_state = '0' and
        e.chk_time is null group by e.bchno ) b on a.bchno = b.bchno and a.chk_seq = b.seq ) f on
        a.audit_bchno = f.bchno <where>
            <if test="id != null "> and a.id = #{id,jdbcType=INTEGER} </if>
            <if
                test="supplierName != null  and supplierName !=''"> and a.supplier_name =
        #{supplierName,jdbcType=VARCHAR} </if>
            <if
                test="chkState != null and chkState != '' "> and a.chk_state =
        #{chkState,jdbcType=VARCHAR} </if>
            <if test="audit != null and audit != ''"> AND exists (
        select e.bchno from mmis_audit_rcdfm e where position(#{chker,jdbcType=VARCHAR} in e.chker)
        > 0 and a.audit_bchno = e.bchno <if
                    test="chkState != null and chkState != '' and chkState == '0'"> and e.chk_state
        = '0' and e.chk_time is null </if> ) </if>
            <if
                test="auditBchno != null and auditBchno != '' "> and a.audit_bchno =
        #{auditBchno,jdbcType=VARCHAR} </if>
            <if test="inStatus != null and inStatus != '' "> and
        a.in_status= #{inStatus,jdbcType=VARCHAR} </if>
            <if test="crter != null and crter != '' ">
        and a.crter = #{crter,jdbcType=VARCHAR} </if>
            <if
                test="createTime != null and createTime != '' "> and a.create_time &lt;=
        #{crteTime,jdbcType=VARCHAR} </if>
            <if test="startTime != null and startTime != ''"> AND
        a.create_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>

            <if
                test="reimStatusFlag != null and reimStatusFlag != '' "> AND a.reim_status_flag =
        #{reimStatusFlag,jdbcType=VARCHAR} </if>
            <if
                test="reimStatusFlag == '4'"> AND (a.reim_status_flag is null ) </if>
            <if
                test="querySettle != null and querySettle != ''"> AND a.settle_period_num is null </if>
            <if
                test="settlePeriodNum != null and settlePeriodNum != ''"> AND a.settle_period_num =
        #{settlePeriodNum,jdbcType=VARCHAR} </if>

        <if
                test="wrhsNeedShow != null and wrhsNeedShow.size() > 0"> AND ( a.wrhs_code IS NULL
        OR a.wrhs_code = '' OR a.wrhs_code IN <foreach collection="wrhsNeedShow" item="wrhsCode"
                    open="(" separator="," close=")"> #{wrhsCode} </foreach> ) </if>
        </where>
        <if
            test="orderBySN == '1'"> order by a.supplier_name asc </if>
        <if test="orderBySN != '1'">
        order by a.create_time desc </if>


    </select>

    <select id="queryWaitGoReimList"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo"> 
        SELECT DISTINCT a.id as id,
        emp.emp_name as appyer, org.org_name as appyOrgId, a.appy_org_id as orgId, a.po_num as
        poNum, a.receipt_num as receiptNum, a.bill_date as billDate, a.supplier_name as
        supplierName, a.docment_num as docmentNum, a.manual_doc_num as manualDocNum, a.track_num as
        trackNum, a.invoice_num as invNum, a.remark as remark, a.wrhs_code as wrhsCode, a.type_code
        as typeCode, inv.name as invType, a.chk_state as chkState, a.audit_bchno as auditBchno,
        a.in_status as inStatus, a.in_emp as inEmp, a.in_time as inTime, a.in_remark as inRemark,
        a.crter as crter, a.create_time as createTime, case when position(#{chker,jdbcType=VARCHAR}
        in f.chker) > 0 then '1' else '0' end as auditFlag, a.hospital_id as hospitalId, a.invo_id
        as invo_Id, a.reim_status_flag as reimStatusFlag, a.att as att, a.att_name as
        att_name,supplier_id as supplierId ,a.settle_period_num as settlePeriodNum,
        <!-- 新增处理人信息字段 -->
        task_emp.emp_name as processEmpName,
        task.exe_emp_code as processEmpCode,
        <!-- 新增总金额字段 -->
        COALESCE(detail_sum.total_amount, 0) as totalAmount
        FROM
        mmis_aset_storage a LEFT JOIN hrm_employee_info emp ON a.appyer = emp.emp_code LEFT JOIN
        hrm_org org ON a.appy_org_id = org.org_id LEFT JOIN mmis_inv_type_cfg inv ON a.type_code =
        inv.type_code LEFT JOIN mmis_invo_rcd invo ON a.invo_id = invo.id ::VARCHAR left join (
        select a.bchno, a.chker from mmis_audit_rcdfm a inner join ( select e.bchno, min(e.chk_seq)
        as seq from mmis_audit_rcdfm e where e.chk_state = '0' and e.chk_time is null group by
        e.bchno ) b on a.bchno = b.bchno and a.chk_seq = b.seq ) f on a.audit_bchno = f.bchno 
        <!-- 连接采购任务表获取处理人信息 -->
        LEFT JOIN (
            SELECT DISTINCT 
                detail.purc_detail_id as storage_detail_id,
                task.exe_emp_code,
                task.item_name
            FROM ecs_reim_purc_task task
            INNER JOIN ecs_reim_purc_task_detail detail ON task.id = detail.task_id
            WHERE task.reim_task_type = '3'
        ) task ON EXISTS (
            SELECT 1 FROM mmis_aset_storage_detail sd 
            WHERE sd.apply_id = a.id AND sd.id = task.storage_detail_id
        )
        LEFT JOIN hrm_employee_info task_emp ON task.exe_emp_code = task_emp.emp_code
        <!-- 连接入库明细总金额计算 -->
        LEFT JOIN (
            SELECT 
                apply_id,
                SUM(COALESCE(amt, price * num, 0)) as total_amount
            FROM mmis_aset_storage_detail 
            WHERE is_deleted IS NULL OR is_deleted != 1
            GROUP BY apply_id
        ) detail_sum ON a.id = detail_sum.apply_id
        <!-- 摘要搜索：连接明细表进行物资名称模糊搜索 -->
        <if test="invtSummaryKeyword != null and invtSummaryKeyword != ''">
        INNER JOIN mmis_aset_storage_detail search_detail ON a.id = search_detail.apply_id 
        AND (search_detail.is_deleted IS NULL OR search_detail.is_deleted != 1)
        AND search_detail.name LIKE CONCAT('%', #{invtSummaryKeyword,jdbcType=VARCHAR}, '%')
        </if>
        <where>
            <if test="id != null "> and a.id = #{id,jdbcType=INTEGER} </if>
            <if
                test="supplierName != null  and supplierName !=''"> and a.supplier_name =
        #{supplierName,jdbcType=VARCHAR} </if>
            <if
                test="chkState != null and chkState != '' "> and a.chk_state =
        #{chkState,jdbcType=VARCHAR} </if>
            <if test="audit != null and audit != ''"> AND exists (
        select e.bchno from mmis_audit_rcdfm e where position(#{chker,jdbcType=VARCHAR} in e.chker)
        > 0 and a.audit_bchno = e.bchno <if
                    test="chkState != null and chkState != '' and chkState == '0'"> and e.chk_state
        = '0' and e.chk_time is null </if> ) </if>
            <if
                test="auditBchno != null and auditBchno != '' "> and a.audit_bchno =
        #{auditBchno,jdbcType=VARCHAR} </if>
            <if test="inStatus != null and inStatus != '' "> and
        a.in_status= #{inStatus,jdbcType=VARCHAR} </if>
            <if test="crter != null and crter != '' ">
        and a.crter = #{crter,jdbcType=VARCHAR} </if>
            <if
                test="createTime != null and createTime != '' "> and a.create_time &lt;=
        #{crteTime,jdbcType=VARCHAR} </if>
            <if test="startTime != null and startTime != ''"> AND
        a.create_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
            <if
                test="reimStatusFlag ==''"> AND (a.reim_status_flag is null ) </if>
            <if
                test="reimStatusFlag != null and reimStatusFlag != '' "> AND a.reim_status_flag =
        #{reimStatusFlag,jdbcType=VARCHAR} </if>

            <if
                test="querySettle != null and querySettle != ''"> AND a.settle_period_num is null </if>
            <if
                test="settlePeriodNum != null and settlePeriodNum != ''"> AND a.settle_period_num =
        #{settlePeriodNum,jdbcType=VARCHAR} </if>

        </where>
        <!-- 新增处理人权限控制 -->
        <if test="processEmpCode != null and processEmpCode != ''">
            AND task.exe_emp_code = #{processEmpCode,jdbcType=VARCHAR}
        </if>
        <if
            test="orderBySN == '1'"> order by a.supplier_name asc </if>
        <if test="orderBySN != '1'">
        order by a.create_time desc </if>

    </select>

    <!--  查询最大单据号+1 ，单据号示例：RK2024030001
        以下sql解释，先拿去当前年月yyyyMM与documen_num的第3开始第6个，就是年月去匹配，
        找最大的单据号，如果为空就自己生成单据号年月+0001，如果有就在此基础上加1为新的单据号
     -->
    <select id="queryDocNum1" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo">
        select case when max(a.docment_num) is null or max(a.docment_num) = '' then
        left(cast(extract(year from current_date) as varchar), 4) || right ( '0' || cast ( extract (
        month from current_date) as varchar), 2) || '0001' else cast ( ( max ( cast ( substring (
        a.docment_num, 3) as int)) + 1) as varchar) end as docmentNum from mmis_aset_storage a where
        (is_deleted is null or is_deleted != 1) and substring(a.docment_num from 3 for 6) =
        left(cast(extract(year from current_date) as varchar), 4) || right('0' || cast(extract(month
        from current_date) as varchar), 2) </select>

    <select id="selectOneByBchno"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo"> select id as id,
        po_num as ponum, receipt_num as receiptnum, bill_date as billdate, supplier_name as
        suppliername, docment_num as docmentnum, manual_doc_num as manualdocnum, track_num as
        tracknum, invoice_num as invonum, remark as remark, appy_org_id as appyorgid, appyer as
        appyer, wrhs_code as wrhscode, type_code as typecode, is_deleted as isdeleted, hospital_id
        as hospitalId, audit_bchno as auditbchno, chk_state as chkstate, crter as crter, create_time
        as createtime, invo_id as invo_id, att as att, att_name as att_name from mmis_aset_storage
        where audit_bchno = #{auditBchno,jdbcType=VARCHAR} </select>

    <!--  查询已入库的物资 这里查询库存actNum于sum表连表的依据是amt 和itemNum ,freight_cost -->
    <select id="queryHasStoragedMats"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo"> SELECT det.id as
        key, det.id , det.apply_id as applyId, det.item_num as itemNum, det.item_num as code,
        sum.num as actNum, b.name, b.ref_price as price, det.hospital_id as hospitalId,
        sto.bill_date as billDate, sto.audit_bchno as storageBchno, b.modspec , b.ref_price as
        refPrice, b.mtr_type as meterUnit, b.aset_brad as asetBrad, b.meter_name as meterUnitName,
        b.name as asetTypeName, b.aset_type as asetType, b.wrhs as wrhsAddr, b.att, b.att_name as
        attName, b.mfgDate, b.easy_code as easyCode, b.exprinDate, b.wrhs_name as wrhsAddrName FROM
        mmis_aset_storage_detail det LEFT JOIN mmis_metering_mode_cfg met ON det.meter_code =
        met.meter_code LEFT JOIN ( SELECT info.code, info.modspec, info.ref_price, info.aset_type,
        info.mtr_type, info.att, info.att_name, info.aset_brad, info.mfg_date as mfgDate, TO_CHAR(
        CASE WHEN info.mfg_date IS NULL THEN NULL ELSE TO_DATE(info.mfg_date, 'yyyy-MM-dd') +
        INTERVAL '1 day' * CAST(COALESCE(info.exprin_date, '0')AS INTEGER) END, 'yyyy-MM-dd' ) AS
        exprinDate, cfg.meter_name, type.name, type.wrhs, wrhs.wrhs_name, info.easy_code FROM
        mmis_aset_info info LEFT JOIN mmis_aset_type type ON info.aset_type = type.code LEFT JOIN
        mmis_wrhs_info wrhs ON type.wrhs = wrhs.wrhs_code LEFT JOIN mmis_metering_mode_cfg cfg ON
        info.mtr_type = cfg.meter_code ) b ON det.item_num =b.code LEFT JOIN mmis_aset_storage sto
        ON det.apply_id = sto.id LEFT JOIN mmis_material_sum sum ON det.item_num = sum.item_num <where>
        det.apply_id IN ( SELECT id FROM mmis_aset_storage WHERE in_status ='1' AND chk_state ='1'
        AND (is_deleted is NULL or is_deleted !=1) ) <if test="name != null and name != ''"> AND
        (det.name like concat('%', #{name,jdbcType=VARCHAR}, '%') or b.easy_code like concat('%',
        #{name,jdbcType=VARCHAR}, '%')) </if>
            <if test="wrhsAddr != null and wrhsAddr != ''"> AND
        b.wrhs =#{wrhsAddr,jdbcType=VARCHAR} </if>
        </where>
    </select>
    <!--  物资申请的查询现有库存物资，少了amt金额的查询  -->
    <select id="queryHasStoragedMatsInApply"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo"> SELECT b.code as
        key,b.code as itemNum, b.code, b.itemname as name, b.modspec, b.ref_price as refPrice,
        b.mtr_type as meterUnit, b.aset_brad as asetBrad, b.meter_name as meterUnitName, b.name as
        asetTypeName, b.aset_type as asetType, b.wrhs as wrhsAddr, b.att, b.att_name as attName,
        b.mfgDate, b.easy_code as easyCode, b.exprinDate, b.base_unit_coefficient as itemCount,
        b.remark, b.wrhs_name as wrhsAddrName,b.hospital_id AS hospitalId, SUM(sum.num) AS actNum
        FROM mmis_material_sum sum LEFT JOIN ( SELECT info.code as code,info.mat_unique_code,
        info.name as itemname, info.modspec, info.ref_price, info.aset_type, info.mtr_type,
        info.att, info.att_name, info.aset_brad, info.mfg_date as mfgDate, TO_CHAR( CASE WHEN
        info.mfg_date IS NULL THEN NULL ELSE TO_DATE(info.mfg_date, 'yyyy-MM-dd') + INTERVAL '1 day'
        * CAST(COALESCE(info.exprin_date, '0')AS INTEGER) END, 'yyyy-MM-dd' ) AS exprinDate,
        cfg.meter_name, cfg.base_unit_coefficient, type.name, type.wrhs, wrhs.wrhs_name,
        info.easy_code,info.remark , info.hospital_id FROM mmis_aset_info_assist info LEFT JOIN
        mmis_aset_type type ON info.aset_type = type.code LEFT JOIN mmis_wrhs_info wrhs ON type.wrhs
        = wrhs.wrhs_code LEFT JOIN mmis_metering_mode_cfg cfg ON info.mtr_type = cfg.meter_code 
        WHERE info.use_status = '1' AND (info.is_deleted IS NULL OR info.is_deleted != 1) ) b
        ON sum.mat_unique_code = b.mat_unique_code <where>
            <if test="name != null and name != ''"> AND (b.itemname like concat('%',
        #{name,jdbcType=VARCHAR}, '%') or b.easy_code like concat('%', #{name,jdbcType=VARCHAR},
        '%')) </if>
            <if test="wrhsCode != null and wrhsCode != ''"> AND b.wrhs
        =#{wrhsCode,jdbcType=VARCHAR} </if>
        </where> GROUP BY b.code, b.itemname, b.modspec,
        b.ref_price, b.mtr_type, b.aset_brad, b.meter_name, b.name, b.aset_type, b.wrhs, b.att,
        b.att_name, b.mfgDate, b.easy_code,b.exprindate, b.base_unit_coefficient, b.remark,
        b.hospital_id, b.wrhs_name </select>

    <!--  物资申请的查询现有库存物资（以matUniqueCode为key）  -->
    <select id="queryHasStoragedMatsInApplyByUniqueCode"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo"> SELECT
        b.mat_unique_code as key, b.mat_unique_code as matUniqueCode, b.code as itemNum, b.code,
        b.itemname as name, b.modspec, b.ref_price as refPrice, b.mtr_type as meterUnit, b.aset_brad
        as asetBrad, b.meter_name as meterUnitName, b.name as asetTypeName, b.aset_type as asetType,
        b.wrhs as wrhsAddr, b.att, b.att_name as attName, b.mfgDate, b.easy_code as easyCode,
        b.exprinDate, b.base_unit_coefficient as itemCount, b.remark, b.wrhs_name as wrhsAddrName,
        b.hospital_id AS hospitalId, SUM(sum.num) AS actNum FROM mmis_material_sum sum LEFT JOIN (
        SELECT info.code as code, info.mat_unique_code, info.name as itemname, info.modspec,
        info.ref_price, info.aset_type, info.mtr_type, info.att, info.att_name, info.aset_brad,
        info.mfg_date as mfgDate, TO_CHAR( CASE WHEN info.mfg_date IS NULL THEN NULL ELSE
        TO_DATE(info.mfg_date, 'yyyy-MM-dd') + INTERVAL '1 day' * CAST(COALESCE(info.exprin_date,
        '0') AS INTEGER) END, 'yyyy-MM-dd' ) AS exprinDate, cfg.meter_name,
        cfg.base_unit_coefficient, type.name, type.wrhs, wrhs.wrhs_name, info.easy_code,
        info.remark, info.hospital_id FROM mmis_aset_info_assist info LEFT JOIN mmis_aset_type type
        ON info.aset_type = type.code LEFT JOIN mmis_wrhs_info wrhs ON type.wrhs = wrhs.wrhs_code
        LEFT JOIN mmis_metering_mode_cfg cfg ON info.mtr_type = cfg.meter_code 
        WHERE info.use_status = '1' ) b ON
        sum.mat_unique_code = b.mat_unique_code <where>
            <if test="name != null and name != ''"> AND (b.itemname like concat('%',
        #{name,jdbcType=VARCHAR}, '%') or b.easy_code like concat('%', #{name,jdbcType=VARCHAR},
        '%')) </if>
            <if test="wrhsCode != null and wrhsCode != ''"> AND b.wrhs =
        #{wrhsCode,jdbcType=VARCHAR} </if>
            <if test="wrhsAddr != null and wrhsAddr != ''"> AND b.wrhs =
                #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if test="matUniqueCode != null and matUniqueCode != ''">
        AND b.mat_unique_code = #{matUniqueCode,jdbcType=VARCHAR} </if>
        </where> GROUP BY
        b.code, b.itemname, b.modspec, b.mat_unique_code, b.ref_price, b.mtr_type, b.aset_brad,
        b.meter_name, b.name, b.aset_type, b.wrhs, b.att, b.att_name, b.mfgDate, b.easy_code,
        b.exprindate, b.base_unit_coefficient, b.remark, b.hospital_id, b.wrhs_name </select>


    <select id="queryStorageRecordsByMat2Code"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo"> SELECT d.id as
        key, d.id, d.apply_id as applyId, d.item_num as itemNum, d.mat_unique_code as matUniqueCode,
        d.num, d.name, d.price, d.amt, d.remark, d.hospital_id as hospitalId, s.docment_num as
        docmentNum, s.appy_org_id as appyOrgId, s.appyer as appyer, s.in_emp as inEmp, s.in_time as
        inTime, s.in_remark as inRemark, s.in_org_id as inOrgId, s.supplier_id as supplierId,
        s.supplier_name AS supplierName FROM mmis_aset_storage_detail d LEFT JOIN mmis_aset_storage
        s ON d.apply_id = s.id <where> s.in_status ='1' AND s.chk_state ='1' AND (s.is_deleted is
        NULL or s.is_deleted !=1) <if test="itemNum != null and itemNum != ''"> AND d.item_num =
        #{itemNum,jdbcType=VARCHAR} </if>
            <if test="matUniqueCode != null and matUniqueCode != ''">
        AND d.mat_unique_code = #{matUniqueCode,jdbcType=VARCHAR} </if>
            <if
                test="appyOrgId != null and appyOrgId != ''"> AND s.appy_org_id =
        #{appyOrgId,jdbcType=VARCHAR} </if>
            <if test="inOrgId != null and inOrgId != ''"> AND
        s.in_org_id = #{inOrgId,jdbcType=VARCHAR} </if>
            <if
                test="supplierId != null and supplierId != ''"> AND s.supplier_id =
        #{supplierId,jdbcType=VARCHAR} </if>
            <if test="supplierName != null and supplierName != ''">
        AND s.supplier_name = #{supplierName,jdbcType=VARCHAR} </if>
            <if
                test="docmentNum != null and docmentNum != ''"> AND s.docment_num =
        #{docmentNum,jdbcType=VARCHAR} </if>
            <if test="inEmp != null and inEmp != ''"> AND s.in_emp =
        #{inEmp,jdbcType=VARCHAR} </if>
            <if test="inTime != null and inTime != ''"> AND s.in_time =
        #{inTime,jdbcType=VARCHAR} </if>
            <if test="inRemark != null and inRemark != ''"> AND
        s.in_remark = #{inRemark,jdbcType=VARCHAR} </if>
            <if
                test="appyTime != null and appyTime != ''"> AND s.appy_time =
        #{appyTime,jdbcType=VARCHAR} </if>
        </where> order by s.in_time asc </select>

    <select id="queryByIds" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo">
        select * from mmis_aset_storage where in_status ='1' AND chk_state ='1' AND (is_deleted is
        NULL or is_deleted !=1) and id in <foreach collection="storageIdList" open="(" separator=","
            close=")" item="exeId"> #{exeId} </foreach>
    </select>

    <select id="selectOneById" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo">
        select * from mmis_aset_storage where id = #{id} and in_status = '1' and chk_state = '1' and
        (is_deleted is NULL or is_deleted != 1) </select>

    <!-- 查询入库明细数据 -->
    <select id="queryStorageDetailList"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo"> SELECT
        s.bill_date AS billDate, s.docment_num as docmentNum, s.manual_doc_num AS manualDocNum, d.id
        AS id, ROW_NUMBER() OVER (PARTITION BY s.docment_num ORDER BY d.id) AS lineNum, tyinfo.code
        AS itemTypeCode, tyinfo.name AS itemTypeName, d.item_num AS itemNum, d.name AS name,
        d.modspec AS modspec, s.audit_bchno AS auditBchno, d.wrhs_addr AS wrhsAddr, d.num AS num,
        d.price AS price, d.amt AS amt, org.org_name AS inOrgName, emp_in.emp_name AS inEmp,
        emp_app.emp_name AS appyer, emp_app.emp_name AS auditor, COALESCE(d.remark, s.remark) AS
        remark, inv_type.name AS invTypeName, s.hospital_id AS hospitalId FROM mmis_aset_storage s
        JOIN mmis_aset_storage_detail d ON s.id = d.apply_id LEFT JOIN (SELECT a.code, a.name,
        a.parent_code FROM mmis_aset_type a WHERE a.is_deleted != 1) tyinfo ON SUBSTRING(d.item_num,
        1, 6) = tyinfo.code LEFT JOIN hrm_org org ON s.in_org_id = org.org_id LEFT JOIN
        mmis_inv_type_cfg inv_type ON s.type_code = inv_type.type_code AND inv_type.inv_type = '1'
        LEFT JOIN hrm_employee_info emp_in ON s.in_emp = emp_in.emp_code LEFT JOIN hrm_employee_info
        emp_app ON s.appyer = emp_app.emp_code WHERE s.is_deleted != 1 AND d.is_deleted != 1 AND
        s.in_status = '1' AND s.chk_state = '1' <if
            test="billDate != null and billDate != ''"> AND s.in_time BETWEEN #{billDate} AND
        #{billDate} </if>
            <if test="startTime != null and startTime != ''"> AND s.in_time BETWEEN
        #{startTime} AND #{endTime} </if>
           
            <if test="manualDocNum != null and manualDocNum != ''"> AND
        s.manual_doc_num = #{manualDocNum} </if>
            <if test="itemNum != null and itemNum != ''"> AND
        d.item_num = #{itemNum} </if>
            <if test="name != null and name != ''"> AND d.name LIKE
        concat('%', #{name}, '%') </if>
            <if test="inOrgId != null and inOrgId != ''"> AND s.in_org_id
        = #{inOrgId} </if>  
            <if test="typeCode != null and typeCode != ''"> AND s.type_code =
        #{typeCode} </if>
            <if
            test="warehouseCodes != null and warehouseCodes.size() > 0"> AND d.wrhs_addr IN <foreach
                collection="warehouseCodes" item="addr" open="(" separator="," close=")"> #{addr} </foreach>
        </if>
        ORDER BY s.in_time, s.manual_doc_num, d.id </select>

    <!-- 查询指定时间段内待结账的入库记录（用于结账预览） -->
    <select id="queryPendingStorageByPeriod" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo">
        select a.id as id, a.po_num as poNum, a.receipt_num as receiptNum, a.bill_date as billDate,
        a.supplier_name as supplierName, a.docment_num as docmentNum, a.manual_doc_num as
        manualDocNum, a.track_num as trackNum, a.invoice_num as invoiceNum, a.remark as remark,
        a.appy_org_id as appyOrgId, a.appyer as appyer, a.wrhs_code as wrhsCode, a.type_code as
        typeCode, a.is_deleted as isDeleted, a.hospital_id as hospitalId, a.audit_bchno as
        auditBchno, a.chk_state as chkState, a.crter as crter, a.create_time as createTime,
        a.invo_id as invoId, a.att as att, a.att_name as attName, a.in_status as inStatus,
        a.in_emp as inEmp, a.in_time as inTime, a.in_remark as inRemark, a.in_org_id as inOrgId,
        a.supplier_id as supplierId, a.settle_period_num as settlePeriodNum,
        org.org_name as appyOrgName, emp.emp_name as appyerName 
        from mmis_aset_storage a 
        LEFT JOIN hrm_org org ON a.appy_org_id = org.org_id 
        LEFT JOIN hrm_employee_info emp ON a.appyer = emp.emp_code 
        <where>
            <!-- 待结账状态：settle_period_num为空 -->
            AND a.settle_period_num IS NULL
            <!-- 未删除 -->
            AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
            <!-- 已审核通过 -->
            AND a.chk_state = '1'
            <!-- 医院过滤 -->
            <if test="hospitalId != null and hospitalId != ''">
                AND a.hospital_id = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <!-- 时间段过滤 -->
            <if test="startDate != null and startDate != ''">
                AND a.bill_date &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND a.bill_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
            <!-- 申请科室过滤 -->
            <if test="appyOrgId != null and appyOrgId != ''">
                AND a.appy_org_id = #{appyOrgId,jdbcType=VARCHAR}
            </if>
            <!-- 供应商名称过滤 -->
            <if test="supplierName != null and supplierName != ''">
                AND a.supplier_name LIKE CONCAT('%', #{supplierName,jdbcType=VARCHAR}, '%')
            </if>
            <!-- 入库状态过滤 -->
            <if test="inStatus != null and inStatus != ''">
                AND a.in_status = #{inStatus,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 信息科入库明细查询 -->
    <select id="queryXinxiStorageDetailList"
        resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo">
        SELECT
        s.bill_date AS billDate,
        s.docment_num as docmentNum,
        s.manual_doc_num AS manualDocNum,
        d.id AS id,
        ROW_NUMBER() OVER (PARTITION BY s.docment_num ORDER BY d.id) AS lineNum,
        tyinfo.code AS itemTypeCode,
        CASE
            WHEN tyinfo.code = '0401' THEN '低值易耗品'
            WHEN tyinfo.code = '0402' THEN '耗材'
            WHEN tyinfo.code = '0403' THEN '配件'
            ELSE tyinfo.name
        END AS itemTypeName,
        d.item_num AS itemNum,
        d.name AS name,
        d.modspec AS modspec,
        s.audit_bchno AS auditBchno,
        d.wrhs_addr AS wrhsAddr,
        d.num AS num,
        d.price AS price,
        d.amt AS amt,
        org.org_name AS inOrgName,
        emp_in.emp_name AS inEmp,
        emp_app.emp_name AS appyer,
        emp_app.emp_name AS auditor,
        COALESCE(d.remark, s.remark) AS remark,
        inv_type.name AS invTypeName,
        s.hospital_id AS hospitalId
        FROM mmis_aset_storage s
        JOIN mmis_aset_storage_detail d ON s.id = d.apply_id
        LEFT JOIN (
            SELECT a.code, a.name, a.parent_code
            FROM mmis_aset_type a
            WHERE a.is_deleted != 1 AND a.parent_code = '04'
        ) tyinfo ON SUBSTRING(d.item_num, 1, 4) = tyinfo.code
        LEFT JOIN hrm_org org ON s.in_org_id = org.org_id
        LEFT JOIN mmis_inv_type_cfg inv_type ON s.type_code = inv_type.type_code AND inv_type.inv_type = '1'
        LEFT JOIN hrm_employee_info emp_in ON s.in_emp = emp_in.emp_code
        LEFT JOIN hrm_employee_info emp_app ON s.appyer = emp_app.emp_code
        LEFT JOIN mmis_aset_type parent_type ON tyinfo.parent_code = parent_type.code
        LEFT JOIN mmis_wrhs_info wrhs ON parent_type.wrhs = wrhs.wrhs_code
        WHERE s.is_deleted != 1 AND d.is_deleted != 1 AND
        s.in_status = '1' AND s.chk_state = '1'
        AND wrhs.wrhs_code = '0003'
        AND tyinfo.code IN ('0401', '0402', '0403')
        <if test="billDate != null and billDate != ''">
            AND s.in_time BETWEEN #{billDate} AND #{billDate}
        </if>
        <if test="startTime != null and startTime != ''">
            AND s.in_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="manualDocNum != null and manualDocNum != ''">
            AND s.manual_doc_num = #{manualDocNum}
        </if>
        <if test="itemNum != null and itemNum != ''">
            AND d.item_num = #{itemNum}
        </if>
        <if test="name != null and name != ''">
            AND d.name LIKE concat('%', #{name}, '%')
        </if>
        <if test="itemTypeCode != null and itemTypeCode != ''">
            AND tyinfo.code = #{itemTypeCode}
        </if>
        ORDER BY s.in_time, s.manual_doc_num, d.id
    </select>

</mapper>