import request from '@/utils/request'
import { RequestType } from '@/types/enums/enums'

/**
 * 新增
 * @param param
 */
export function addMmisMaterialSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/save',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 删除
 * @param param
 */
export function deleteMmisMaterialSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/delete',
    method: RequestType.DEL,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function updateMmisMaterialSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/update',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 查询
 * @param param
 */
export function queryMmisMaterialSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/list',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询
 * @param param
 */
export function queryMmisMaterialSumList(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryList',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 入库物资汇总
 */
export function queryMmisMatReceiptSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMatReceiptSum',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 信息科库房入库汇总 🏥💻
 */
export function queryCustomReceiptSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryCustomReceiptSum',
    method: RequestType.POST,
    data: param,
  })
}
/**
 * 出库物资汇总
 */
export function queryMmisMatOutSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMatOutSum',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 信息科库房出库汇总 🏥💻
 */
export function queryCustomOutSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryCustomOutSum',
    method: RequestType.POST,
    data: param,
  })
}
/**
 * 低值易耗品汇总
 */
export function queryMmisMatLowPriceSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMatLowPriceSum',
    method: RequestType.POST,
    data: param,
  })
}
/**
 * 物资类目汇总账
 */
export function queryMmisMatCatalogSum(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMatCatalogSum',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 工作台物资申领、入出库、库存金额与数量汇总
 */
export function queryAllAmtNumInMyCtrl(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryAllAmtNumInMyCtrl',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 当前低于库存阈值的物资信息
 */
export function lessThanReplenishThreshold(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/lessThanReplenishThreshold',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询物资分类金额占比
 */
export function queryMatTypeAmtRatio(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMatTypeAmtRatio',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询物资对账数据 🧾
 */
export function queryStockCheck(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryStockCheck',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询月度理论金额 💰
 */
export function queryMonthlyTheoreticalAmount(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryMonthlyTheoreticalAmount',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询物资对账差异详情 📊
 */
export function queryStockCheckDetail(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryStockCheckDetail',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询物资库存差异分析 📊
 */
export function queryStockAnalysis(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryStockAnalysis',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 调整库存 📦
 */
export function adjustStock(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/adjustStock',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询精度转换分析详情 🔍
 */
export function queryPrecisionAnalysisDetail(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryPrecisionAnalysisDetail',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询精度转换分析汇总 📊
 */
export function queryPrecisionAnalysisSummary(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/queryPrecisionAnalysisSummary',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 执行精度转换 ⚙️
 */
export function convertPrecision(param: Object) {
  return request({
    url: 'mmis/mmisMaterialSum/convertPrecision',
    method: RequestType.POST,
    data: param,
  })
}

// ==================== 增强版精度分析API ====================

/**
 * 查询精度转换分析详情 - 增强版 🔍✨
 */
export function queryPrecisionAnalysisDetailEnhanced(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/queryPrecisionAnalysisDetailEnhanced',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询精度转换分析汇总 - 增强版 📊✨
 */
export function queryPrecisionAnalysisSummaryEnhanced(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/queryPrecisionAnalysisSummaryEnhanced',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 精度转换预检查 🔍
 */
export function preConversionCheck(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/preConversionCheck',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 增强的批量精度转换 ⚙️✨
 */
export function enhancedBatchConvert(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/enhancedBatchConvert',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询转换进度 📈
 */
export function queryProgress(batchNo: string) {
  return request({
    url: `mmis/precisionAnalysis/queryProgress/${batchNo}`,
    method: RequestType.GET,
  })
}

/**
 * 快速回滚
 * @param batchNo 批次号
 */
export function quickRollback(batchNo: string) {
  return request({
    url: 'mmis/precisionAnalysis/quickRollback',
    method: 'post',
    params: { batchNo }
  })
}

/**
 * 恢复原本精度（恢复到6位小数）
 * @param param 恢复参数
 */
export function restoreOriginalPrecision(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/restoreOriginalPrecision',
    method: 'post',
    data: param
  })
}

/**
 * 选择性恢复 🎯
 */
export function selectiveRestore(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/selectiveRestore',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询风险等级分析 ⚠️
 */
export function queryRiskLevelAnalysis(param: Object) {
  return request({
    url: 'mmis/precisionAnalysis/queryRiskLevelAnalysis',
    method: RequestType.POST,
    data: param,
  })
}
