package com.jp.med.mmis.modules.matSum.mapper.read;

import com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumCheckVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumMonthlyTheoreticalVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumDetailVO;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisDetailVo;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisSummaryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
@Mapper
public interface MmisMaterialSumReadMapper extends BaseMapper<MmisMaterialSumDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<MmisMaterialSumVo> queryList(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> querySum1(MmisMaterialSumDto sumDto);

    List<MmisMaterialSumVo> listSearch(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatReceiptSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryCustomReceiptSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryCustomOutSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatOutSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatLowPriceSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatCatalogSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryAll(MmisMaterialSumDto sumDto);

    MmisMaterialSumVo queryStorageAmtNum(MmisMaterialSumDto dto);

    MmisMaterialSumVo queryOutboundAmtNum(MmisMaterialSumDto dto);

    MmisMaterialSumVo queryApplyAmtNum(MmisMaterialSumDto dto);

    MmisMaterialSumVo queryStockAmtNum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryStockWarning(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatTypeAmtRatio(MmisMaterialSumDto dto);

    /**
     * 查询物资对账数据
     */
    List<MmisMaterialSumCheckVO> queryStockCheck(MmisMaterialSumDto dto);

    /**
     * 查询月度理论金额数据
     * 从2024年12月到当前月份的理论金额、入库金额、出库金额
     */
    List<MmisMaterialSumMonthlyTheoreticalVO> queryMonthlyTheoreticalAmount(MmisMaterialSumDto dto);

    /**
     * 查询物资对账差异详情
     * 根据物资唯一编码查询对账差异详情
     */
    List<MmisMaterialSumDetailVO> queryStockCheckDetail(MmisMaterialSumDto dto);

    /**
     * 根据物资唯一编码查询库存
     * 
     * @param matUniqueCode 物资唯一编码
     * @return 库存信息
     */
    MmisMaterialSumDto selectByMatUniqueCode(@Param("matUniqueCode") String matUniqueCode);

    /**
     * 查询精度转换分析详情
     */
    List<PrecisionAnalysisDetailVo> queryPrecisionAnalysisDetail(MmisMaterialSumDto dto);

    /**
     * 查询精度转换分析汇总
     */
    PrecisionAnalysisSummaryVo queryPrecisionAnalysisSummary(MmisMaterialSumDto dto);

    /**
     * 数据完整性检查
     * 检查assist表中use_status='1'的数据在info表和sum表的存在情况
     * 
     * @return 数据完整性检查结果列表
     */
    List<Map<String, Object>> checkDataIntegrity();
}
