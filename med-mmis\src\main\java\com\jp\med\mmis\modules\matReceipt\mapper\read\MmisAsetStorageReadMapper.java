package com.jp.med.mmis.modules.matReceipt.mapper.read;

import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 物资入库
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 15:01:07
 */
@Mapper
public interface MmisAsetStorageReadMapper extends BaseMapper<MmisAsetStorageDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<MmisAsetStorageVo> queryList(MmisAsetStorageDto dto);

    MmisAsetStorageVo queryDocNum(MmisAsetStorageDto dto);

    MmisAsetStorageVo selectOneByBchno(String auditBchno);

    MmisAsetStorageVo queryDocNum1();

    List<MmisAsetStorageDetailVo> queryHasStoragedMats(MmisAsetStorageDto dto);

    List<MmisAsetStorageDetailVo> queryHasStoragedMatsInApply(MmisAsetStorageDto dto);

    /**
     * 根据物资唯一编码查询现有库存物资
     *
     * @param dto 查询参数
     * @return 物资库存列表
     */
    List<MmisAsetStorageDetailVo> queryHasStoragedMatsInApplyByUniqueCode(MmisAsetStorageDto dto);

    List<MmisAsetStorageDetailVo> queryStorageRecordsByMat2Code(MmisAsetStorageDto dto);

    List<MmisAsetStorageVo> queryByIds(MmisAsetStorageDto dto);

    MmisAsetStorageVo selectOneById(Integer applyId);

    List<MmisAsetStorageVo> queryWaitGoReimList(MmisAsetStorageDto dto);

    /**
     * 查询指定时间段内待结账的入库记录（用于结账预览）
     *
     * @param dto 查询参数，包含startDate、endDate等
     * @return 入库记录列表
     */
    List<MmisAsetStorageVo> queryPendingStorageByPeriod(MmisAsetStorageDto dto);

    /**
     * 查询入库明细数据
     *
     * @param dto 查询参数
     * @return 入库明细列表
     */
    List<MmisAsetStorageDetailVo> queryStorageDetailList(MmisAsetStorageDto dto);

    /**
     * 信息科入库明细查询
     *
     * @param dto 查询参数
     * @return 信息科入库明细列表
     */
    List<MmisAsetStorageDetailVo> queryXinxiStorageDetailList(MmisAsetStorageDto dto);
}
