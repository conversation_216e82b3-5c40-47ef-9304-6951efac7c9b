package com.jp.med.mmis.modules.matSum.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumCheckVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumMonthlyTheoreticalVO;
import com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumDetailVO;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisDetailVo;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisSummaryVo;

import java.util.List;
import java.util.Map;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
public interface MmisMaterialSumReadService extends IService<MmisMaterialSumDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<MmisMaterialSumVo> queryList(MmisMaterialSumDto dto);

    /**
     * 汇总界面列表
     * 
     * @param dto
     * @return
     */
    List<MmisMaterialSumVo> listSearch(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatReceiptSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryCustomReceiptSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryCustomOutSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatOutSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatLowPriceSum(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatCatalogSum(MmisMaterialSumDto dto);

    MmisMaterialSumVo queryAllAmtNumInMyCtrl(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> lessThanReplenishThreshold(MmisMaterialSumDto dto);

    List<MmisMaterialSumVo> queryMatTypeAmtRatio(MmisMaterialSumDto dto);

    /**
     * 查询物资对账数据
     */
    List<MmisMaterialSumCheckVO> queryStockCheck(MmisMaterialSumDto dto);

    /**
     * 查询月度理论金额数据
     * 从2024年12月到当前月份的理论金额、入库金额、出库金额
     */
    List<MmisMaterialSumMonthlyTheoreticalVO> queryMonthlyTheoreticalAmount(MmisMaterialSumDto dto);

    /**
     * 查询物资对账差异详情
     */
    List<MmisMaterialSumDetailVO> queryStockCheckDetail(MmisMaterialSumDto dto);

    /**
     * 查询指定物资的库存差异分析信息
     * 
     * @param dto 包含物资唯一编码的参数对象
     * @return 包含库存差异分析的Map对象
     */
    Map<String, Object> queryStockAnalysis(MmisMaterialSumDto dto);

    /**
     * 查询精度转换分析详情
     */
    List<PrecisionAnalysisDetailVo> queryPrecisionAnalysisDetail(MmisMaterialSumDto dto);

    /**
     * 查询精度转换分析汇总
     */
    PrecisionAnalysisSummaryVo queryPrecisionAnalysisSummary(MmisMaterialSumDto dto);

    /**
     * 数据完整性检查
     * 检查assist表中use_status='1'的数据在info表和sum表的存在情况
     */
    List<Map<String, Object>> checkDataIntegrity();

	MmisMaterialSumDto selectByMatUniqueCode(String matUniqueCode);
}
